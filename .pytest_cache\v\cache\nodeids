["tests/test_fields.py::TestBaseField::test_default_value", "tests/test_fields.py::TestBaseField::test_field_creation", "tests/test_fields.py::TestBaseField::test_validation_optional", "tests/test_fields.py::TestBaseField::test_validation_required", "tests/test_fields.py::TestBooleanField::test_boolean_validation", "tests/test_fields.py::TestCharField::test_char_creation", "tests/test_fields.py::TestCharField::test_char_no_size", "tests/test_fields.py::TestCharField::test_char_validation", "tests/test_fields.py::TestDateField::test_date_validation", "tests/test_fields.py::TestDatetimeField::test_datetime_validation", "tests/test_fields.py::TestFloatField::test_float_sql_type", "tests/test_fields.py::TestFloatField::test_float_validation", "tests/test_fields.py::TestHtmlField::test_html_validation", "tests/test_fields.py::TestIntegerField::test_integer_validation", "tests/test_fields.py::TestJsonField::test_json_validation", "tests/test_fields.py::TestMany2oneField::test_many2one_validation", "tests/test_fields.py::TestSelectionField::test_dynamic_selection", "tests/test_fields.py::TestSelectionField::test_selection_items", "tests/test_fields.py::TestSelectionField::test_selection_validation"]