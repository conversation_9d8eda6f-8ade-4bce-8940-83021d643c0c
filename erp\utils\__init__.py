"""
Utilities package
"""
try:
    from .responses import APIResponse, ModelResponse, handle_database_error, handle_generic_error
    from .handlers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestValidator, AuthenticationHandler
    from .middleware import database_middleware, timing_middleware, error_handling_middleware, logging_middleware
    _web_utils_available = True
except ImportError:
    # Web utilities not available (missing FastAPI dependencies)
    _web_utils_available = False

# Schema utilities are always available
from .schema import SchemaGenerator, SchemaComparator

if _web_utils_available:
    __all__ = [
        'APIResponse', 'ModelResponse', 'handle_database_error', 'handle_generic_error',
        'ModelRequestHandler', 'RequestValidator', 'AuthenticationHandler',
        'database_middleware', 'timing_middleware', 'error_handling_middleware', 'logging_middleware',
        'SchemaGenerator', 'SchemaComparator'
    ]
else:
    __all__ = ['SchemaGenerator', 'SchemaComparator']
