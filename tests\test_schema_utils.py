"""
Tests for schema generation and comparison utilities
"""
import asyncio
import sys
import os

# Add the ERP core to Python path
erp_path = os.path.join(os.path.dirname(__file__), '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.utils.schema import <PERSON><PERSON>a<PERSON><PERSON>ator, SchemaComparator
from erp.models.base import BaseModel, ModelRegistry
from erp.fields import Char, Text, Integer, Boolean, Selection, Many2one

# Try to import async components
try:
    from erp.models.async_base import AsyncBaseModel, AsyncModelRegistry
    from erp.database.async_registry import AsyncDatabaseRegistry
    _async_available = True
except ImportError:
    _async_available = False
    AsyncBaseModel = None
    AsyncModelRegistry = None
    AsyncDatabaseRegistry = None


# Test models for schema testing
class TestModel(BaseModel):
    """Test model for schema generation"""
    _name = 'test.model'
    _description = 'Test Model'
    _table = 'test_model'
    
    description = Text(string='Description', help='Test description field')
    active = Boolean(string='Active', default=True, required=True)
    sequence = Integer(string='Sequence', default=10)
    category = Selection([
        ('draft', 'Draft'),
        ('published', 'Published'),
    ], string='Category', default='draft')


# Only create async test model if async is available
if _async_available and AsyncBaseModel:
    class AsyncTestModel(AsyncBaseModel):
        """Async test model for schema generation"""
        _name = 'async.test.model'
        _description = 'Async Test Model'
        _table = 'async_test_model'

        description = Text(string='Description')
        active = Boolean(string='Active', default=True, required=True, index=True)
        sequence = Integer(string='Sequence', default=10)
        parent_id = Many2one('async.test.model', string='Parent')
else:
    AsyncTestModel = None


class TestSchemaGenerator:
    """Test cases for SchemaGenerator"""
    
    def test_get_model_schema_sync(self):
        """Test schema generation for sync models"""
        schema = SchemaGenerator.get_model_schema('test.model', use_async=False)
        
        assert schema is not None
        assert schema['model_name'] == 'test.model'
        assert schema['table_name'] == 'test_model'
        assert schema['description'] == 'Test Model'
        
        # Check fields
        fields = schema['fields']
        assert 'id' in fields
        assert 'name' in fields
        assert 'description' in fields
        assert 'active' in fields
        assert 'sequence' in fields
        assert 'category' in fields
        
        # Check field properties
        assert fields['active']['required'] is True
        assert fields['active']['type'] == 'boolean'
        assert fields['active']['sql_type'] == 'BOOLEAN'
        assert fields['active']['default'] is True
        
        assert fields['sequence']['type'] == 'integer'
        assert fields['sequence']['sql_type'] == 'INTEGER'
        assert fields['sequence']['default'] == 10
        
        assert fields['category']['type'] == 'selection'
        assert fields['category']['selection'] == [('draft', 'Draft'), ('published', 'Published')]
        
        # Check constraints
        constraints = schema['constraints']
        assert len(constraints) == 1
        assert constraints[0]['type'] == 'PRIMARY KEY'
        assert constraints[0]['columns'] == ['id']
    
    def test_get_model_schema_async(self):
        """Test schema generation for async models"""
        if not _async_available:
            print("Skipping async test - async dependencies not available")
            return

        schema = SchemaGenerator.get_model_schema('async.test.model', use_async=True)

        assert schema is not None
        assert schema['model_name'] == 'async.test.model'
        assert schema['table_name'] == 'async_test_model'
        assert schema['description'] == 'Async Test Model'

        # Check fields
        fields = schema['fields']
        assert 'parent_id' in fields
        assert fields['parent_id']['type'] == 'many2one'
        assert fields['parent_id']['comodel_name'] == 'async.test.model'

        # Check indexes
        indexes = schema['indexes']
        active_index = next((idx for idx in indexes if 'active' in idx['columns']), None)
        assert active_index is not None
        assert active_index['name'] == 'idx_async_test_model_active'
    
    def test_get_model_schema_nonexistent(self):
        """Test schema generation for non-existent model"""
        schema = SchemaGenerator.get_model_schema('nonexistent.model', use_async=False)
        assert schema is None
    
    def test_generate_create_table_sql_sync(self):
        """Test CREATE TABLE SQL generation for sync models"""
        sql = SchemaGenerator.generate_create_table_sql('test.model', use_async=False)
        
        assert sql is not None
        assert 'CREATE TABLE IF NOT EXISTS test_model' in sql
        assert 'id TEXT NOT NULL' in sql
        assert 'name TEXT NOT NULL' in sql
        assert 'description TEXT' in sql
        assert 'active BOOLEAN NOT NULL DEFAULT TRUE' in sql
        assert 'sequence INTEGER DEFAULT 10' in sql
        assert 'category VARCHAR(255) DEFAULT \'draft\'' in sql
        assert 'PRIMARY KEY (id)' in sql
    
    def test_generate_create_table_sql_async(self):
        """Test CREATE TABLE SQL generation for async models"""
        if not _async_available:
            print("Skipping async test - async dependencies not available")
            return

        sql = SchemaGenerator.generate_create_table_sql('async.test.model', use_async=True)

        assert sql is not None
        assert 'CREATE TABLE IF NOT EXISTS async_test_model' in sql
        assert 'parent_id VARCHAR(255)' in sql
        assert 'CREATE INDEX IF NOT EXISTS idx_async_test_model_active ON async_test_model (active)' in sql
    
    def test_get_all_models_schema(self):
        """Test getting schema for all models"""
        schemas = SchemaGenerator.get_all_models_schema(use_async=False)
        
        assert isinstance(schemas, dict)
        assert 'test.model' in schemas
        
        # Check that each schema is valid
        for model_name, schema in schemas.items():
            assert schema['model_name'] == model_name
            assert 'fields' in schema
            assert 'table_name' in schema

    def test_schema_generator_integration(self):
        """Test integration between SchemaGenerator components"""
        # Test that schema generation and SQL generation are consistent
        schema = SchemaGenerator.get_model_schema('test.model', use_async=False)
        sql = SchemaGenerator.generate_create_table_sql('test.model', use_async=False)

        assert schema is not None
        assert sql is not None

        # Check that all fields with SQL types appear in the SQL
        for field_name, field_info in schema['fields'].items():
            if field_info['sql_type'] is not None:
                assert field_name in sql

    def test_field_type_mapping(self):
        """Test that field types are correctly mapped to SQL types"""
        schema = SchemaGenerator.get_model_schema('test.model', use_async=False)
        fields = schema['fields']

        # Test specific field type mappings
        assert fields['id']['sql_type'] == 'TEXT'  # Char field
        assert fields['description']['sql_type'] == 'TEXT'  # Text field
        assert fields['active']['sql_type'] == 'BOOLEAN'  # Boolean field
        assert fields['sequence']['sql_type'] == 'INTEGER'  # Integer field
        assert fields['category']['sql_type'] == 'VARCHAR(255)'  # Selection field

    def test_default_value_handling(self):
        """Test that default values are correctly handled"""
        schema = SchemaGenerator.get_model_schema('test.model', use_async=False)
        fields = schema['fields']

        # Test different types of default values
        assert fields['active']['default'] is True
        assert fields['sequence']['default'] == 10
        assert fields['category']['default'] == 'draft'

        # Test SQL generation with defaults
        sql = SchemaGenerator.generate_create_table_sql('test.model', use_async=False)
        assert 'DEFAULT TRUE' in sql
        assert 'DEFAULT 10' in sql
        assert "DEFAULT 'draft'" in sql


class TestSchemaComparator:
    """Test cases for SchemaComparator"""
    
    async def test_types_compatible(self):
        """Test type compatibility checking"""
        # Test compatible types
        assert SchemaComparator._types_compatible('TEXT', 'TEXT')
        assert SchemaComparator._types_compatible('TEXT', 'CHARACTER VARYING')
        assert SchemaComparator._types_compatible('VARCHAR(255)', 'TEXT')
        assert SchemaComparator._types_compatible('INTEGER', 'BIGINT')
        assert SchemaComparator._types_compatible('BOOLEAN', 'BOOLEAN')
        assert SchemaComparator._types_compatible('TIMESTAMP', 'TIMESTAMP WITHOUT TIME ZONE')
        
        # Test incompatible types
        assert not SchemaComparator._types_compatible('TEXT', 'INTEGER')
        assert not SchemaComparator._types_compatible('BOOLEAN', 'TEXT')
        assert not SchemaComparator._types_compatible('DATE', 'TIMESTAMP')
    
    async def test_compare_model_with_table_missing_model(self):
        """Test comparison with non-existent model"""
        if not _async_available:
            print("Skipping async comparison test - async dependencies not available")
            return

        result = await SchemaComparator.compare_model_with_table('nonexistent.model')

        assert result['status'] == 'error'
        assert 'not found' in result['message']
    

    
    def test_field_type_mapping(self):
        """Test that field types are correctly mapped to SQL types"""
        schema = SchemaGenerator.get_model_schema('test.model', use_async=False)
        fields = schema['fields']
        
        # Test specific field type mappings
        assert fields['id']['sql_type'] == 'TEXT'  # Char field
        assert fields['description']['sql_type'] == 'TEXT'  # Text field
        assert fields['active']['sql_type'] == 'BOOLEAN'  # Boolean field
        assert fields['sequence']['sql_type'] == 'INTEGER'  # Integer field
        assert fields['category']['sql_type'] == 'VARCHAR(255)'  # Selection field
    
    def test_default_value_handling(self):
        """Test that default values are correctly handled"""
        schema = SchemaGenerator.get_model_schema('test.model', use_async=False)
        fields = schema['fields']
        
        # Test different types of default values
        assert fields['active']['default'] is True
        assert fields['sequence']['default'] == 10
        assert fields['category']['default'] == 'draft'
        
        # Test SQL generation with defaults
        sql = SchemaGenerator.generate_create_table_sql('test.model', use_async=False)
        assert 'DEFAULT TRUE' in sql
        assert 'DEFAULT 10' in sql
        assert "DEFAULT 'draft'" in sql


# Test runner function
def run_tests():
    """Run all schema utility tests"""
    print("Running schema utility tests...")
    
    # Create test instances to register models
    test_model = TestModel()
    if _async_available and AsyncTestModel:
        async_test_model = AsyncTestModel()
    
    # Run sync tests
    test_generator = TestSchemaGenerator()
    
    try:
        print("Testing sync model schema generation...")
        test_generator.test_get_model_schema_sync()
        print("✓ Sync model schema generation works")
        
        print("Testing async model schema generation...")
        test_generator.test_get_model_schema_async()
        if _async_available:
            print("✓ Async model schema generation works")
        else:
            print("✓ Async model schema generation skipped (dependencies not available)")
        
        print("Testing non-existent model handling...")
        test_generator.test_get_model_schema_nonexistent()
        print("✓ Non-existent model handling works")
        
        print("Testing CREATE TABLE SQL generation...")
        test_generator.test_generate_create_table_sql_sync()
        test_generator.test_generate_create_table_sql_async()
        print("✓ CREATE TABLE SQL generation works")
        
        print("Testing all models schema retrieval...")
        test_generator.test_get_all_models_schema()
        print("✓ All models schema retrieval works")
        
        print("Testing schema integration...")
        test_generator.test_schema_generator_integration()
        print("✓ Schema integration works")
        
        print("Testing field type mapping...")
        test_generator.test_field_type_mapping()
        print("✓ Field type mapping works")
        
        print("Testing default value handling...")
        test_generator.test_default_value_handling()
        print("✓ Default value handling works")
        
        # Run async tests
        test_comparator = TestSchemaComparator()
        
        print("Testing type compatibility...")
        asyncio.run(test_comparator.test_types_compatible())
        print("✓ Type compatibility checking works")

        print("Testing missing model comparison...")
        asyncio.run(test_comparator.test_compare_model_with_table_missing_model())
        if _async_available:
            print("✓ Missing model comparison works")
        else:
            print("✓ Missing model comparison skipped (dependencies not available)")
        
        print("\n✅ All schema utility tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    run_tests()
